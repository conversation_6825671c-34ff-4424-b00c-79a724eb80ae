import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from 'react';

export interface GiftItem {
  id: number;
  name: string;
  price?: string;
  quantity?: number;
  description?: string;
  link?: string;
  image: string;
  mostWanted?: boolean;
}

interface GiftItemsContextType {
  giftItems: GiftItem[];
  addGiftItem: (item: Omit<GiftItem, 'id'>) => void;
  removeGiftItem: (id: number) => void;
  updateGiftItem: (id: number, updates: Partial<GiftItem>) => void;
  clearGiftItems: () => void;
  setGiftItems: (items: GiftItem[]) => void;
  getTotalValue: () => number;
  getItemCount: () => number;
}

const GiftItemsContext = createContext<GiftItemsContextType | undefined>(
  undefined
);

interface GiftItemsProviderProps {
  children: ReactNode;
  initialGiftItems?: GiftItem[];
}

export const GiftItemsProvider: React.FC<GiftItemsProviderProps> = ({
  children,
  initialGiftItems = [],
}) => {
  const [giftItems, setGiftItemsState] = useState<GiftItem[]>(initialGiftItems);

  const addGiftItem = useCallback((item: Omit<GiftItem, 'id'>) => {
    const newItem: GiftItem = {
      ...item,
      id: Date.now() + Math.random(), // Ensure unique ID
    };
    setGiftItemsState((prev) => [...prev, newItem]);
  }, []);

  const removeGiftItem = useCallback((id: number) => {
    setGiftItemsState((prev) => prev.filter((item) => item.id !== id));
  }, []);

  const updateGiftItem = useCallback(
    (id: number, updates: Partial<GiftItem>) => {
      setGiftItemsState((prev) =>
        prev.map((item) => (item.id === id ? { ...item, ...updates } : item))
      );
    },
    []
  );

  const clearGiftItems = useCallback(() => {
    setGiftItemsState([]);
  }, []);

  const setGiftItems = useCallback((items: GiftItem[]) => {
    setGiftItemsState(items);
  }, []);

  const getTotalValue = useCallback(() => {
    return giftItems.reduce((total, item) => {
      const price = parseFloat(item.price?.replace(/,/g, '') || '0') || 0;
      const quantity = item.quantity || 1;
      return total + (price * quantity);
    }, 0);
  }, [giftItems]);

  const getItemCount = useCallback(() => {
    return giftItems.length;
  }, [giftItems]);

  const value: GiftItemsContextType = {
    giftItems,
    addGiftItem,
    removeGiftItem,
    updateGiftItem,
    clearGiftItems,
    setGiftItems,
    getTotalValue,
    getItemCount,
  };

  return (
    <GiftItemsContext.Provider value={value}>
      {children}
    </GiftItemsContext.Provider>
  );
};

export const useGiftItems = (): GiftItemsContextType => {
  const context = useContext(GiftItemsContext);
  if (context === undefined) {
    throw new Error('useGiftItems must be used within a GiftItemsProvider');
  }
  return context;
};
